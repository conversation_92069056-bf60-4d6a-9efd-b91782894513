package com.rutong.medical.admin.vo.defence;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备选择 VO
 * <AUTHOR>
 * @Date 2025-07-29
 */
@Data
@ApiModel(value = "设备选择视图对象")
public class DeviceSelectVO {

    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID")
    private Long id;

    /**
     * 设备编号
     */
    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /**
     * 业务系统编号
     */
    @ApiModelProperty(value = "业务系统编号")
    private String businessCode;

    /**
     * 安装位置全名称
     */
    @ApiModelProperty(value = "安装位置全名称")
    private String spaceFullName;

    /**
     * 在线状态(1:在线,0:离线)
     */
    @ApiModelProperty(value = "在线状态(1:在线,0:离线)")
    private Integer isOnline;

    /**
     * 设备SN
     */
    @ApiModelProperty(value = "设备SN")
    private String deviceSn;

    /**
     * 是否已关联到防区（true:已关联，false:未关联）
     */
    @ApiModelProperty(value = "是否已关联到防区")
    private Boolean isAssociated;

    /**
     * 关联的防区ID（如果已关联）
     */
    @ApiModelProperty(value = "关联的防区ID")
    private Long associatedDefenceId;

    /**
     * 关联的防区名称（如果已关联）
     */
    @ApiModelProperty(value = "关联的防区名称")
    private String associatedDefenceName;
}

package com.rutong.medical.admin.dto.defence;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 防区新增/编辑 DTO
 * <AUTHOR>
 * @Date 2025-07-29
 */
@Data
@ApiModel(value = "防区新增/编辑请求参数")
public class DefenceSaveDTO {

    /**
     * 防区ID（编辑时必填，新增时为空）
     */
    @ApiModelProperty(value = "防区ID（编辑时必填，新增时为空）", example = "1")
    private Long id;

    /**
     * 防区编号
     */
    @ApiModelProperty(value = "防区编号", required = true, example = "FQ001")
    @NotBlank(message = "防区编号不能为空")
    @Size(max = 30, message = "防区编号长度不能超过30个字符")
    private String defenceCode;

    /**
     * 防区名称
     */
    @ApiModelProperty(value = "防区名称", required = true, example = "一楼大厅防区")
    @NotBlank(message = "防区名称不能为空")
    @Size(max = 50, message = "防区名称长度不能超过50个字符")
    private String defenceName;

    /**
     * 防区状态（0:撤防,1:布防）
     */
    @ApiModelProperty(value = "防区状态（0:撤防,1:布防）", example = "0")
    private Integer defenceState;

    /**
     * 开始时间（自动布防时使用）
     */
    @ApiModelProperty(value = "开始时间（自动布防时使用）", example = "2025-07-29T08:00:00")
    private LocalDateTime startTime;

    /**
     * 结束时间（自动布防时使用）
     */
    @ApiModelProperty(value = "结束时间（自动布防时使用）", example = "2025-07-29T18:00:00")
    private LocalDateTime endTime;

    /**
     * 关联的设备ID列表
     */
    @ApiModelProperty(value = "关联的设备ID列表", example = "[1, 2, 3]")
    private List<Long> deviceIds;
}

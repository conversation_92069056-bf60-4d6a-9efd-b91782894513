package com.rutong.medical.admin.dto.defence;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 防区查询 DTO
 * <AUTHOR>
 * @Date 2025-07-29
 */
@Data
@ApiModel(value = "防区查询请求参数")
public class DefenceQueryDTO {

    /**
     * 防区编号
     */
    @ApiModelProperty(value = "防区编号", example = "FQ001")
    private String defenceCode;

    /**
     * 防区名称
     */
    @ApiModelProperty(value = "防区名称", example = "一楼大厅防区")
    private String defenceName;
}

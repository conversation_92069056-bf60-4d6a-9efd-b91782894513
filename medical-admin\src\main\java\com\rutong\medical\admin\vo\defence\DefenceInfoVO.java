package com.rutong.medical.admin.vo.defence;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 防区信息 VO
 * <AUTHOR>
 * @Date 2025-07-29
 */
@Data
@ApiModel(value = "防区信息视图对象")
public class DefenceInfoVO {

    /**
     * 防区ID
     */
    @ApiModelProperty(value = "防区ID")
    private Long id;

    /**
     * 防区编号
     */
    @ApiModelProperty(value = "防区编号")
    private String defenceCode;

    /**
     * 防区名称
     */
    @ApiModelProperty(value = "防区名称")
    private String defenceName;
}
